
import { useState, useEffect, useRef } from 'react';
import { X, ShoppingCart, Trash2 } from 'lucide-react';
import { Table, MenuItem, OrderItem } from '../types/pos';
import MenuCategoryComponent from './MenuCategory';
import MenuItemComponent from './MenuItem';

interface OrderModalProps {
  table?: Table;
  isOpen: boolean;
  onClose: () => void;
  onPlaceOrder: (tableId: string, items: OrderItem[], customer: any) => void;
  onPayOrder?: (table: Table, orderId?: string) => void;
  orderType?: 'table' | 'online' | 'takeaway';
  menuItems: MenuItem[];
  menuCategories: { id: string; name: string; icon?: string }[];
}

const OrderModal = ({ table, isOpen, onClose, onPlaceOrder, onPayOrder, orderType = 'table', menuItems, menuCategories }: OrderModalProps) => {
  const [selectedCategory, setSelectedCategory] = useState(menuCategories[0]?.id || '');
  const [orderItems, setOrderItems] = useState<{ [key: string]: number }>({});
  const [notes, setNotes] = useState<{ [key: string]: string }>({});
  const [customerName, setCustomerName] = useState('');
  const [customerQuery, setCustomerQuery] = useState('');
  const [customerOptions, setCustomerOptions] = useState<any[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<any | null>(null);
  const [isAddingNewCustomer, setIsAddingNewCustomer] = useState(false);
  const [customerPhone, setCustomerPhone] = useState('');
  const customerInputRef = useRef<HTMLInputElement>(null);
  const customerPhoneInputRef = useRef<HTMLInputElement>(null);
  const [customerError, setCustomerError] = useState('');
  const [discounts, setDiscounts] = useState<{ [key: string]: number }>({});
  const [discountTypes, setDiscountTypes] = useState<{ [key: string]: 'percent' | 'nominal' }>({});
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const activeOrders = table && Array.isArray(table.orders) ? table.orders : [];
  const selectedOrder = activeOrders.find(o => o.id === selectedOrderId) || activeOrders[0];

  // Reset selectedOrderId jika table/activeOrders berubah
  useEffect(() => {
    setSelectedOrderId(activeOrders[0]?.id || null);
  }, [table, activeOrders.length]);

  useEffect(() => {
    if (customerQuery.length > 1) {
      fetch(`/api/customers?search=${encodeURIComponent(customerQuery)}`)
        .then(res => res.json())
        .then(data => setCustomerOptions(data));
    } else {
      setCustomerOptions([]);
    }
  }, [customerQuery]);

  useEffect(() => {
    if (menuCategories.length > 0 && !selectedCategory) {
      setSelectedCategory(menuCategories[0].id);
    }
  }, [menuCategories]);

  const handleCustomerSelect = (customer: any) => {
    setSelectedCustomer(customer);
    setCustomerName(customer.name);
    setIsAddingNewCustomer(false);
  };

  const handleAddNewCustomer = async () => {
    setCustomerError('');
    if (!customerName) return;
    try {
      const res = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: customerName, phone: customerPhone })
      });
      if (res.ok) {
        const newCustomer = await res.json();
        setSelectedCustomer(newCustomer);
        setCustomerName(newCustomer.name);
        setIsAddingNewCustomer(false);
        setCustomerPhone('');
      } else {
        setCustomerError('Gagal menambah customer.');
      }
    } catch (e) {
      setCustomerError('Tidak dapat terhubung ke server.');
    }
  };

  if (!isOpen) return null;
  const isTableOrder = orderType === 'table';

  // Cek apakah ada pesanan aktif (status apapun, karena tidak ada 'completed' di type)
  // const activeOrders = table && Array.isArray(table.orders) ? table.orders : [];
  // const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  // const selectedOrder = activeOrders.find(o => o.id === selectedOrderId) || activeOrders[0];

  // Perbaiki filter kategori menu
  const filteredItems = selectedCategory
    ? menuItems.filter(item => {
        if (typeof item.category === 'object' && item.category !== null && (item.category as { id?: string }).id) {
          return (item.category as { id: string }).id === selectedCategory;
        }
        return item.category === selectedCategory;
      })
    : menuItems;

  const handleQuantityChange = (itemId: string, quantity: number) => {
    setOrderItems(prev => ({
      ...prev,
      [itemId]: quantity
    }));
  };

  const handleNotesChange = (itemId: string, note: string) => {
    setNotes(prev => ({
      ...prev,
      [itemId]: note
    }));
  };

  const handleDiscountChange = (itemId: string, value: number) => {
    setDiscounts(prev => ({ ...prev, [itemId]: value }));
  };
  const handleDiscountTypeChange = (itemId: string, type: 'percent' | 'nominal') => {
    setDiscountTypes(prev => ({ ...prev, [itemId]: type }));
  };

  const getOrderTotal = () => {
    return Object.entries(orderItems).reduce((total, [itemId, quantity]) => {
      const item = menuItems.find(i => i.id === itemId);
      if (!item) return total;
      const discount = discounts[itemId] || 0;
      const discountType = discountTypes[itemId] || 'nominal';
      let price = item.price;
      if (discountType === 'percent') {
        price = price - (price * discount / 100);
      } else {
        price = price - discount;
      }
      return total + (price * quantity);
    }, 0);
  };

  const getOrderItemsForSubmit = (): OrderItem[] => {
    return Object.entries(orderItems)
      .filter(([_, quantity]) => quantity > 0)
      .map(([itemId, quantity]) => {
        const menuItem = menuItems.find(i => i.id === itemId)!;
        return {
          id: `${itemId}-${Date.now()}`,
          menuItem,
          quantity,
          notes: notes[itemId] || undefined,
          discount: discounts[itemId] || 0,
          discountType: discountTypes[itemId] || 'nominal',
        };
      });
  };

  const handlePlaceOrder = async () => {
    const items = getOrderItemsForSubmit();
    if (items.length > 0) {
      let customerData = null;
      let customerToUse = selectedCustomer;
      // If no customer is selected but a name is entered, create the customer first
      if (!selectedCustomer && customerName) {
        try {
          const res = await fetch('/api/customers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: customerName, phone: customerPhone })
          });
          if (res.ok) {
            customerToUse = await res.json();
          }
        } catch (e) {
          // Optionally show error to user
        }
      }
      if (customerToUse) {
        customerData = { customerId: customerToUse.id };
      } else if (customerName) {
        customerData = { customer: { name: customerName, phone: customerPhone } };
      }
      if (isTableOrder && table) {
        onPlaceOrder(table.id, items, customerData);
      } else if (orderType === 'online') {
        onPlaceOrder('online-order', items, customerData);
      } else if (orderType === 'takeaway') {
        onPlaceOrder('takeaway', items, customerData);
      }
      onClose();
      setOrderItems({});
      setNotes({});
      setCustomerName('');
      setSelectedCustomer(null);
      setCustomerQuery('');
      setCustomerOptions([]);
      setIsAddingNewCustomer(false);
      setCustomerPhone('');
    }
  };

  const clearOrder = () => {
    setOrderItems({});
    setNotes({});
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-3xl shadow-2xl w-full max-w-5xl max-h-[90vh] flex flex-col overflow-hidden animate-slide-in">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-400 to-green-700 text-white px-8 py-6 flex items-center justify-between relative">
          <div>
            <h1 className="text-2xl font-bold drop-shadow">
              {table?.status === 'occupied' ? 'Tambah Pesanan - Meja' : 'Pesanan Meja'} {table?.number}
            </h1>
            <p className="text-sm opacity-90 font-medium">
              {table?.seats} kursi tersedia
              {table?.status === 'occupied' && table?.orders && table?.orders.length > 0 && (
                <span className="ml-2">• {table.orders.length} pesanan aktif</span>
              )}
            </p>
          </div>
          <button onClick={onClose} className="bg-white/20 hover:bg-white/30 w-10 h-10 rounded-full flex items-center justify-center text-white text-2xl font-bold transition-all backdrop-blur">
            ×
          </button>
        </div>

        {/* Existing Orders Section for Occupied Tables */}
        {table?.status === 'occupied' && table?.orders && table?.orders.length > 0 && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 px-8 py-4">
            <h3 className="text-sm font-bold text-blue-900 mb-3">📋 Pesanan yang Sudah Ada:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {table.orders.map(order => (
                <div key={order.id} className="bg-white rounded-lg p-3 border border-blue-200 shadow-sm">
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-xs font-bold text-blue-800">#{order.noStruk}</span>
                    <span className="text-xs font-semibold text-green-700">Rp{order.total.toLocaleString('id-ID')}</span>
                  </div>
                  <div className="text-xs text-gray-600">
                    {order.customer?.name && <div>Customer: {order.customer.name}</div>}
                    <div className="flex justify-between items-center mt-1">
                      <span>Status: <span className="capitalize font-medium">{order.status}</span></span>
                      {order.createdBy && <span>oleh: {order.createdBy}</span>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Body */}
        <div className="flex-1 flex flex-col md:flex-row gap-8 p-8 overflow-y-auto">
          {/* Kiri: Menu & Customer */}
          <div className="flex-1 min-w-0">
            {/* Customer Section */}
            <div className="bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl p-6 mb-6 border border-slate-200">
              <label className="text-xs font-semibold text-slate-500 uppercase mb-2 block tracking-wide">Nama Customer</label>
              <div className="flex gap-4 relative">
                <div className="flex-1 relative">
                  <input
                    id="customerName"
                    type="text"
                    className="flex-1 border-2 border-slate-200 rounded-lg p-4 text-base font-medium focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all bg-white w-full"
                    placeholder="Cari atau tambah nama customer"
                    value={isAddingNewCustomer ? customerName : (selectedCustomer ? selectedCustomer.name : customerQuery)}
                    onChange={e => {
                      setCustomerQuery(e.target.value);
                      setCustomerName(e.target.value);
                      setIsAddingNewCustomer(true);
                      setSelectedCustomer(null);
                    }}
                    onFocus={() => setIsAddingNewCustomer(true)}
                    ref={customerInputRef}
                    autoComplete="off"
                  />
                  {/* Dropdown search results */}
                  {isAddingNewCustomer && customerQuery.length > 1 && (
                    <div className="absolute left-0 right-0 mt-1 bg-white border border-slate-200 rounded-lg shadow-lg z-20 max-h-48 overflow-y-auto">
                      {customerOptions.length > 0 ? (
                        customerOptions.map(option => (
                          <div
                            key={option.id}
                            className="px-4 py-2 cursor-pointer hover:bg-green-100"
                            onClick={() => handleCustomerSelect(option)}
                          >
                            {option.name} {option.phone && <span className="text-xs text-slate-400 ml-2">{option.phone}</span>}
                          </div>
                        ))
                      ) : (
                        <div
                          className="px-4 py-2 cursor-pointer hover:bg-green-100 text-green-600 font-semibold"
                          onClick={async () => {
                            setIsAddingNewCustomer(true);
                            setSelectedCustomer(null);
                            if (customerName && customerPhone) {
                              await handleAddNewCustomer();
                            } else if (customerPhoneInputRef.current) {
                              customerPhoneInputRef.current.focus();
                            }
                          }}
                        >
                          + Customer
                        </div>
                      )}
                    </div>
                  )}
                </div>
                {isAddingNewCustomer && (
                  <input
                    id="customerPhone"
                    type="tel"
                    className="flex-1 border-2 border-slate-200 rounded-lg p-4 text-base font-medium focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all bg-white"
                    placeholder="Nomor Telepon (opsional)"
                    value={customerPhone}
                    onChange={e => setCustomerPhone(e.target.value)}
                    autoComplete="off"
                    ref={customerPhoneInputRef}
                  />
                )}
                {isAddingNewCustomer && customerName && (
                  <button
                    type="button"
                    className="mt-2 px-4 py-2 rounded bg-green-500 text-white font-semibold hover:bg-green-600 transition"
                    onClick={handleAddNewCustomer}
                  >
                    Simpan Customer
                  </button>
                )}
              </div>
              {customerError && <div className="text-red-600 text-sm mt-1">{customerError}</div>}
            </div>
            {/* Kategori Tabs */}
            <div className="flex gap-2 mb-6 overflow-x-auto pb-2 category-tabs">
              {menuCategories.map(category => (
                <button
                  key={category.id}
                  className={`px-5 py-2 rounded-full font-semibold text-sm flex items-center gap-2 transition-all category-tab ${selectedCategory === category.id ? 'bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg active' : 'bg-slate-100 text-slate-600 hover:bg-slate-200'}`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.icon && <span>{category.icon}</span>}
                  {category.name}
                </button>
              ))}
                    </div>
            {/* Menu Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 menu-grid mb-6">
              {filteredItems.map(item => (
                <div key={item.id} className="bg-white rounded-xl p-5 border border-slate-100 shadow hover:shadow-lg transition-all menu-item relative overflow-hidden">
                  <h3 className="font-bold text-lg text-slate-800 mb-2 menu-item-name">{item.name}</h3>
                  <p className="text-sm text-slate-500 mb-3 menu-item-description">{item.description}</p>
                  <div className="text-green-600 font-bold text-xl mb-4 menu-item-price">Rp {item.price.toLocaleString('id-ID')}</div>
                  <div className="flex items-center gap-3 quantity-controls">
                    <button className="quantity-btn minus" onClick={() => handleQuantityChange(item.id, Math.max((orderItems[item.id] || 0) - 1, 0))}>−</button>
                    <span className="quantity-display">{orderItems[item.id] || 0}</span>
                    <button className="quantity-btn plus" onClick={() => handleQuantityChange(item.id, (orderItems[item.id] || 0) + 1)}>+</button>
                  </div>
                </div>
              ))}
            </div>
                </div>
          {/* Kanan: Ringkasan Pesanan */}
          <div className="w-full md:w-[340px] flex-shrink-0 order-summary bg-gradient-to-br from-slate-50 to-slate-200 rounded-xl p-6 border border-slate-200 flex flex-col">
            <div className="flex items-center gap-3 mb-5 order-summary-header">
              <span className="text-2xl">🛒</span>
              <h3 className="font-bold text-lg text-slate-800 order-summary-title">Ringkasan Pesanan</h3>
                </div>
            {Object.keys(orderItems).every(key => orderItems[key] === 0) ? (
              <div className="empty-cart text-center py-10 text-slate-400">
                <div className="empty-cart-icon text-5xl mb-3">🛍️</div>
                <div className="empty-cart-text text-base font-medium">Belum ada item dipilih</div>
              </div>
            ) : (
              <>
                <div className="space-y-4 mb-6">
                  {Object.entries(orderItems)
                    .filter(([_, quantity]) => quantity > 0)
                    .map(([itemId, quantity]) => {
                      const item = menuItems.find(i => i.id === itemId)!;
                      return (
                        <div key={itemId} className="flex justify-between items-center bg-white rounded-lg p-3 border border-slate-100">
                          <span className="font-medium text-slate-800">{item.name} <span className="text-xs text-slate-400">x{quantity}</span></span>
                          <span className="font-bold text-green-600">Rp {(item.price * quantity).toLocaleString('id-ID')}</span>
                        </div>
                      );
                    })}
                </div>
                <div className="flex justify-between items-center border-t pt-4 mb-4">
                  <span className="font-bold text-lg">Total:</span>
                  <span className="font-extrabold text-xl text-slate-800">Rp {getOrderTotal().toLocaleString('id-ID')}</span>
                      </div>
              </>
            )}
                    <div className="flex flex-col gap-3 mt-4">
                  <button
                    type="button"
                    onClick={handlePlaceOrder}
                    className="w-full flex items-center justify-center gap-2 py-3 rounded-xl text-lg font-bold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg hover:from-green-500 hover:to-green-700 active:scale-95 transition-all duration-150"
                  >
                    <span role="img" aria-label="pesan">🛒</span> Pesan Sekarang
                  </button>
                  <button
                    type="button"
                    disabled={getOrderTotal() === 0}
                    className={`w-full flex items-center justify-center gap-2 py-3 rounded-xl text-lg font-bold shadow-lg transition-all duration-150
                      ${getOrderTotal() === 0
                        ? 'bg-gray-300 text-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-400 to-blue-600 text-white hover:from-blue-500 hover:to-blue-700 active:scale-95'}`}
                    onClick={async () => {
                      if (getOrderTotal() === 0) return;
                      await handlePlaceOrder();
                      if (onPayOrder && table) {
                        onPayOrder(table, selectedOrderId || undefined);
                      }
                    }}
                  >
                    <span role="img" aria-label="bayar">💳</span> Pembayaran
                  </button>
                </div>
              </div>
            </div>
      </div>
    </div>
  );
};

export default OrderModal;
