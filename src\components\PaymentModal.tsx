import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Button } from './ui/button';
import { Table } from '../types/pos';
import { CreditCard, Banknote, Smartphone } from 'lucide-react';
import { formatCurrency } from '../utils/posData';

interface PaymentModalProps {
  open: boolean;
  table: Table | null;
  orderId?: string; // order yang dipilih untuk dibayar
  onClose: () => void;
  onSuccess: (tableId: string, orderId?: string) => void;
}

const PaymentModal = ({ open, table, orderId, onClose, onSuccess }: PaymentModalProps) => {
  const [amount, setAmount] = useState('');
  const [error, setError] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<'cash' | 'card' | 'qr'>('cash');

  useEffect(() => {
    if (open) {
      setAmount('');
      setError('');
    }
  }, [open]);

  // Cari order yang dipilih
  const order = orderId && table?.orders ? table.orders.find(o => o.id === orderId) : (table?.orders && table.orders[0]);
  if (!table || !order) return null;
  const total = order.total;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (Number(amount) < total) {
      setError('Jumlah bayar kurang dari total.');
      return;
    }
    setError('');
    onSuccess(table.id, orderId);
  };

  const denominations = [5000, 10000, 20000, 50000, 100000];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] p-0 bg-gradient-to-br from-indigo-400 via-purple-400 to-blue-300 border-0 shadow-2xl rounded-2xl overflow-hidden">
        {/* Hidden accessibility elements */}
        <DialogTitle className="sr-only">Sistem Pembayaran</DialogTitle>
        <DialogDescription className="sr-only">
          Proses pembayaran untuk meja {table?.number} dengan total {formatCurrency(total)}
        </DialogDescription>
        <div className="w-full h-full bg-white/90 backdrop-blur-lg rounded-2xl p-3 sm:p-4 md:p-6 flex flex-col gap-4 md:gap-6 overflow-y-auto">
          <div className="text-center mb-3 md:mb-4">
            <h1 className="text-xl sm:text-2xl md:text-3xl font-extrabold text-gray-800 flex items-center justify-center gap-2">
              <span role='img' aria-label='dining'>🍽️</span> Sistem Pembayaran Modern
            </h1>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 payment-layout flex-1 min-h-0">
            {/* LEFT: Detail Pesanan */}
            <div className="flex flex-col gap-3 md:gap-4 left-section overflow-y-auto">
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 sm:p-4 md:p-5 shadow order-section">
                <div className="text-base md:text-lg font-bold flex items-center gap-2 mb-3 md:mb-4 order-title">
                  <span role='img' aria-label='clipboard'>📋</span> Detail Pesanan
                </div>
                {(order.orderItems || []).map(item => (
                  <div key={item.id} className="flex justify-between items-center py-1.5 md:py-2 border-b border-gray-200 order-item last:border-b-0">
                    <span className="font-medium text-gray-700 text-sm md:text-base item-name">{item.quantity}x {item.menuItem.name}</span>
                    <span className="font-semibold text-gray-800 text-sm md:text-base item-price">Rp {(item.menuItem.price * item.quantity).toLocaleString('id-ID')}</span>
                  </div>
                ))}
                <div className="flex justify-between items-center py-1.5 md:py-2 border-b border-gray-200 order-item">
                  <span className="item-name text-sm md:text-base">Diskon (10%)</span>
                  <span className="item-price discount text-green-600 text-sm md:text-base">-Rp {(total * 0.10).toLocaleString('id-ID')}</span>
                </div>
                <div className="flex justify-between items-center py-1.5 md:py-2 order-item">
                  <span className="item-name text-sm md:text-base">Pajak (11%)</span>
                  <span className="item-price tax text-red-500 text-sm md:text-base">Rp {(total * 0.11).toLocaleString('id-ID')}</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-blue-400 to-blue-600 text-white rounded-xl p-3 sm:p-4 md:p-5 text-center shadow-lg total-section">
                <div className="text-sm md:text-base opacity-90 mb-1 total-label">Total Pembayaran</div>
                <div className="text-xl sm:text-2xl md:text-3xl font-extrabold total-amount">Rp {(total * 0.91 * 1.11).toLocaleString('id-ID')}</div>
              </div>
            </div>
            {/* RIGHT: Metode Pembayaran & Uang Diterima */}
            <div className="flex flex-col gap-3 md:gap-4 right-section overflow-y-auto">
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 sm:p-4 md:p-5 shadow payment-methods">
                <div className="text-base md:text-lg font-bold flex items-center gap-2 mb-3 md:mb-4 method-title">
                  <span role='img' aria-label='card'>💳</span> Metode Pembayaran
                </div>
                <div className="grid grid-cols-3 gap-2 md:gap-3 method-grid mb-3">
                  <button
                    type="button"
                    onClick={() => setSelectedMethod('cash')}
                    className={`method-btn flex flex-col items-center justify-center gap-1 p-3 md:p-4 rounded-lg border-2 font-bold text-sm md:text-base transition-all duration-200 shadow-sm ${selectedMethod === 'cash' ? 'active border-blue-600 bg-blue-100' : 'border-gray-200 bg-white hover:border-blue-400 hover:bg-blue-50'}`}
                  >
                    <span className="method-icon text-lg md:text-xl">💰</span>
                    <span className="method-label">Tunai</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedMethod('card')}
                    className={`method-btn flex flex-col items-center justify-center gap-1 p-3 md:p-4 rounded-lg border-2 font-bold text-sm md:text-base transition-all duration-200 shadow-sm ${selectedMethod === 'card' ? 'active border-blue-600 bg-blue-100' : 'border-gray-200 bg-white hover:border-blue-400 hover:bg-blue-50'}`}
                  >
                    <span className="method-icon text-lg md:text-xl">💳</span>
                    <span className="method-label">Kartu</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedMethod('qr')}
                    className={`method-btn flex flex-col items-center justify-center gap-1 p-3 md:p-4 rounded-lg border-2 font-bold text-sm md:text-base transition-all duration-200 shadow-sm ${selectedMethod === 'qr' ? 'active border-blue-600 bg-blue-100' : 'border-gray-200 bg-white hover:border-blue-400 hover:bg-blue-50'}`}
                  >
                    <span className="method-icon text-lg md:text-xl">📱</span>
                    <span className="method-label">QR Code</span>
                  </button>
                </div>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 sm:p-4 md:p-5 shadow amount-input">
                {selectedMethod === 'cash' && (
                  <>
                    <div className="text-base md:text-lg font-bold flex items-center gap-2 mb-3 amount-label">
                      <span role='img' aria-label='money'>💵</span> Uang Diterima
                    </div>
                    <div className="amount-display bg-white border-2 border-gray-200 rounded-lg p-3 md:p-4 text-xl md:text-2xl font-extrabold text-center text-gray-800 mb-4">
                      {amount ? parseInt(amount).toLocaleString('id-ID') : '0'}
                    </div>
                    <div className="grid grid-cols-3 gap-2 amount-buttons mb-3">
                      {[5000, 10000, 20000, 50000, 100000, 200000].map(nom => (
                        <button
                          type="button"
                          key={nom}
                          className="amount-btn bg-gray-200 hover:bg-blue-200 text-sm md:text-base font-bold rounded-lg px-1 py-2 md:py-3 transition-all duration-150"
                          onClick={() => setAmount(prev => (parseInt(prev || '0') + nom).toString())}
                        >
                          {nom.toLocaleString('id-ID')}
                        </button>
                      ))}
                    </div>
                    <button
                      type="button"
                      onClick={() => setAmount('')}
                      className="w-full bg-red-100 hover:bg-red-200 text-red-700 font-bold rounded-lg py-2 md:py-3 mb-3 text-sm md:text-base transition-all duration-150"
                    >
                      Hapus / Clear
                    </button>
                    <div className={`change-section rounded-lg p-3 md:p-4 text-center border-2 ${amount && parseInt(amount) >= total ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300'} mb-2`}>
                      <div className={`change-label text-sm md:text-base font-bold mb-1 ${amount && parseInt(amount) >= total ? 'text-green-600' : 'text-red-500'}`}>Kembalian</div>
                      <div className={`change-amount text-lg md:text-xl font-extrabold ${amount && parseInt(amount) >= total ? 'text-green-700' : 'text-red-600'}`}>
                        Rp {amount ? (parseInt(amount) - total).toLocaleString('id-ID') : '0'}
                      </div>
                    </div>
                    {error && <div className="text-red-600 text-sm md:text-base mt-2 font-bold">{error}</div>}
                  </>
                )}
                {selectedMethod === 'card' && (
                  <>
                    <div className="text-base md:text-lg font-bold flex items-center gap-2 mb-3 amount-label">
                      <span role='img' aria-label='card'>💳</span> Nomor Kartu
                    </div>
                    <input
                      type="text"
                      inputMode="numeric"
                      maxLength={20}
                      placeholder="Masukkan nomor kartu"
                      className="w-full bg-white border-2 border-gray-200 rounded-lg p-3 md:p-4 text-lg md:text-xl font-extrabold text-center text-gray-800 mb-4 focus:ring-2 focus:ring-blue-400"
                    />
                    <div className="mb-4">
                      <label className="block text-sm md:text-base font-bold mb-2">Upload Foto Bukti Pembayaran</label>
                      <input
                        type="file"
                        accept="image/*"
                        className="block w-full text-sm md:text-base border-2 border-gray-200 rounded-lg p-2 md:p-3 bg-white file:mr-2 file:py-1 file:px-2 md:file:py-2 md:file:px-3 file:rounded file:border-0 file:text-sm md:file:text-base file:font-bold file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200"
                      />
                    </div>
                  </>
                )}
                {selectedMethod === 'qr' && (
                  <>
                    <div className="text-base md:text-lg font-bold flex items-center gap-2 mb-3 amount-label">
                      <span role='img' aria-label='qr'>📱</span> Upload Bukti Pembayaran QR Code
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm md:text-base font-bold mb-2">Upload Foto Bukti Pembayaran</label>
                      <input
                        type="file"
                        accept="image/*"
                        className="block w-full text-sm md:text-base border-2 border-gray-200 rounded-lg p-2 md:p-3 bg-white file:mr-2 file:py-1 file:px-2 md:file:py-2 md:file:px-3 file:rounded file:border-0 file:text-sm md:file:text-base file:font-bold file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200"
                      />
                    </div>
                  </>
                )}
              </div>
              <button
                type="submit"
                onClick={handleSubmit}
                className="process-btn w-full bg-gradient-to-r from-green-400 to-green-600 text-white rounded-lg py-3 md:py-4 text-lg md:text-xl font-extrabold mt-2 shadow-lg transition-all duration-200 hover:from-green-500 hover:to-green-700 flex items-center justify-center gap-2"
              >
                <span role='img' aria-label='sparkles'>✨</span> Proses Pembayaran
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal; 